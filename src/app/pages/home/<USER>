import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TabsModule } from 'primeng/tabs';
import { TooltipModule } from 'primeng/tooltip';
import { Observable, Subscription } from 'rxjs';

import { SearchService } from 'app/services/search/search.service';
import * as SearchActions from 'app/store/search/search.actions';
import * as fromSearch from 'app/store/search/search.selectors';

interface SelectOption {
  label: string;
  value: string;
}

/**
 * Custom validator to ensure start date is before end date
 */
function dateRangeValidator(control: any): any {
  const startDate = control.get('startDate')?.value;
  const endDate = control.get('endDate')?.value;

  if (!startDate || !endDate) {
    return null; // Don't validate if either date is missing
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start >= end) {
    return { dateRangeInvalid: { message: 'Start date must be before end date' } };
  }

  return null;
}

@Component({
  selector: 'app-home',
  imports: [
    RouterModule,
    CommonModule,
    ReactiveFormsModule,
    TabsModule,
    CheckboxModule,
    InputTextModule,
    SelectModule,
    ButtonModule,
    CardModule,
    IconFieldModule,
    InputIconModule,
    SelectButtonModule,
    TooltipModule,
    CalendarModule,
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
})
export class HomeComponent implements OnInit, OnDestroy {
  searchTypeOptions: SelectOption[] = [
    { label: 'EDI Record', value: 'edi' },
    { label: 'App Record', value: 'app' },
  ];
  selectedSearchType: string = 'edi';

  directionOptions: SelectOption[] = [
    { label: 'Inbound & Outbound', value: 'inbound-outbound' },
    { label: 'Inbound', value: 'inbound' },
    { label: 'Outbound', value: 'outbound' },
  ];
  selectedDirection: string = 'inbound-outbound';

  transactionViewOptions: SelectOption[] = [
    { label: 'All Transaction Sets', value: 'all' },
    { label: 'Group View', value: 'group' },
  ];

  selectedTransactionView = 'all';
  selectedEquipmentSearchType = 'equipment';

  inboundFields = [
    { label: 'ISA Sender', placeholder: 'ISA Sender', value: '', key: 'isaSender' },
    { label: 'GS Sender', placeholder: 'GS Sender', value: '', key: 'gsSender' },
  ];

  outboundFields = [
    { label: 'ISA Receiver', placeholder: 'ISA Receiver', value: '', key: 'isaReceiver' },
    { label: 'GS Receiver', placeholder: 'GS Receiver', value: '', key: 'gsReceiver' },
  ];

  // Sidebar filter options
  sidebarInboundOptions = [
    { label: 'ISA Receiver', value: 'isaReceiver' },
    { label: 'GS Receiver', value: 'gsReceiver' },
  ];

  sidebarOutboundOptions = [
    { label: 'ISA Sender', value: 'isaSender' },
    { label: 'GS Sender', value: 'gsSender' },
  ];

  sidebarEdiAttributesOptions = [
    { label: 'Message ID', value: 'messageId' },
    { label: 'Network', value: 'network' },
    { label: 'Message Status', value: 'messageStatus' },
    { label: 'Control Number', value: 'controlNumber' },
  ];

  // Date range for the search form - now using separate start and end dates
  // Initialize end date as current time
  endDate: Date = new Date();
  // Initialize start date as 15 days ago from current time
  startDate: Date = new Date(new Date().setDate(new Date().getDate() - 15));

  // Form group for the search form
  searchForm!: FormGroup;

  // Loading state
  isLoading = false;

  // NgRx selectors
  formValues$: Observable<any> = inject(Store).select(fromSearch.selectFormValues);
  hasSearched$: Observable<boolean> = inject(Store).select(fromSearch.selectHasSearched);

  // Subscriptions
  private subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private store: Store,
    // Inject SearchService for potential future direct API calls
    private searchService: SearchService,
  ) {}

  ngOnInit() {
    this.initForm();

    // Subscribe to form values from the store
    this.subscriptions.add(
      this.formValues$.subscribe(formValues => {
        if (formValues && this.searchForm) {
          // Update the form with values from the store
          this.updateFormFromState(formValues);
        }
      }),
    );

    // Subscribe to query parameters to handle direct URL navigation
    this.subscriptions.add(
      this.route.queryParams.subscribe(params => {
        if (Object.keys(params).length > 0 && this.searchForm) {
          // Extract sidebar filter values from query parameters
          const formValues: any = {};

          // Default values
          const defaultInboundFilters = {
            isaReceiver: false,
            gsReceiver: false,
          };

          const defaultOutboundFilters = {
            isaSender: false,
            gsSender: false,
          };

          const defaultEdiAttributesFilters = {
            messageId: false,
            network: false,
            messageStatus: false,
            controlNumber: false,
          };

          // Create new objects with default values
          const sidebarInboundFilters = { ...defaultInboundFilters };
          const sidebarOutboundFilters = { ...defaultOutboundFilters };
          const sidebarEdiAttributesFilters = { ...defaultEdiAttributesFilters };

          // Helper function to convert string 'true'/'false' to boolean
          const stringToBoolean = (value: string): boolean => value === 'true';

          // Process all query parameters
          Object.keys(params).forEach(key => {
            // Process sidebar inbound filters
            if (key.startsWith('filter_inbound_')) {
              const filterKey = key.replace('filter_inbound_', '');
              if (filterKey in sidebarInboundFilters) {
                sidebarInboundFilters[filterKey] = stringToBoolean(params[key]);
              }
            }
            // Process sidebar outbound filters
            else if (key.startsWith('filter_outbound_')) {
              const filterKey = key.replace('filter_outbound_', '');
              if (filterKey in sidebarOutboundFilters) {
                sidebarOutboundFilters[filterKey] = stringToBoolean(params[key]);
              }
            }
            // Process sidebar EDI attributes filters
            else if (key.startsWith('filter_edi_')) {
              const filterKey = key.replace('filter_edi_', '');
              if (filterKey in sidebarEdiAttributesFilters) {
                sidebarEdiAttributesFilters[filterKey] = stringToBoolean(params[key]);
              }
            }
          });

          // Only update the form if we have sidebar filter values
          if (
            Object.keys(sidebarInboundFilters).length > 0 ||
            Object.keys(sidebarOutboundFilters).length > 0 ||
            Object.keys(sidebarEdiAttributesFilters).length > 0
          ) {
            formValues.sidebarInboundFilters = sidebarInboundFilters;
            formValues.sidebarOutboundFilters = sidebarOutboundFilters;
            formValues.sidebarEdiAttributesFilters = sidebarEdiAttributesFilters;

            // Update the form with the extracted values
            this.updateFormFromState(formValues);
          }
        }
      }),
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.unsubscribe();
  }

  /**
   * Initialize the reactive form
   */
  private initForm(): void {
    // Create form arrays for inbound and outbound fields
    const inboundFieldsArray = this.fb.array(
      this.inboundFields.map(field =>
        this.fb.group({
          label: [field.label],
          placeholder: [field.placeholder],
          value: [''],
          key: [field.key],
        }),
      ),
    );

    const outboundFieldsArray = this.fb.array(
      this.outboundFields.map(field =>
        this.fb.group({
          label: [field.label],
          placeholder: [field.placeholder],
          value: [''],
          key: [field.key],
        }),
      ),
    );

    // Create the main form group
    this.searchForm = this.fb.group(
      {
        searchType: [this.selectedSearchType],
        startDate: [this.startDate],
        endDate: [this.endDate],
        direction: [this.selectedDirection],
        transactionView: [this.selectedTransactionView],
        equipmentSearchType: [this.selectedEquipmentSearchType],
        equipmentIds: [''],
        transactionAttributes: [''],
        inboundFields: inboundFieldsArray,
        outboundFields: outboundFieldsArray,
        // Sidebar filter checkboxes
        sidebarInboundFilters: this.fb.group({
          isaReceiver: [false],
          gsReceiver: [false],
        }),
        sidebarOutboundFilters: this.fb.group({
          isaSender: [false],
          gsSender: [false],
        }),
        sidebarEdiAttributesFilters: this.fb.group({
          messageId: [false],
          network: [false],
          messageStatus: [false],
          controlNumber: [false],
        }),
        // Dynamic text inputs for sidebar checkboxes
        dynamicInboundInputs: this.fb.group({
          isaReceiver: [''],
          gsReceiver: [''],
        }),
        dynamicOutboundInputs: this.fb.group({
          isaSender: [''],
          gsSender: [''],
        }),
        dynamicEdiAttributesInputs: this.fb.group({
          messageId: [''],
          network: [''],
          messageStatus: [''],
          controlNumber: [''],
        }),
      },
      { validators: [dateRangeValidator] },
    );

    // Subscribe to date changes to provide real-time validation feedback
    this.searchForm.get('startDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    this.searchForm.get('endDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    // Subscribe to sidebar checkbox changes to clear corresponding text inputs when unchecked
    this.setupSidebarCheckboxSubscriptions();
  }

  /**
   * Setup subscriptions for sidebar checkbox changes to clear text inputs when unchecked
   */
  private setupSidebarCheckboxSubscriptions(): void {
    // Inbound filters
    this.sidebarInboundOptions.forEach(option => {
      const subscription = this.searchForm
        .get(`sidebarInboundFilters.${option.value}`)
        ?.valueChanges.subscribe(checked => {
          // Handle both boolean and array values
          const isChecked = Array.isArray(checked)
            ? checked.length > 0 && checked[0] === true
            : !!checked;
          if (!isChecked) {
            this.searchForm.get(`dynamicInboundInputs.${option.value}`)?.setValue('');
          }
        });
      if (subscription) {
        this.subscriptions.add(subscription);
      }
    });

    // Outbound filters
    this.sidebarOutboundOptions.forEach(option => {
      const subscription = this.searchForm
        .get(`sidebarOutboundFilters.${option.value}`)
        ?.valueChanges.subscribe(checked => {
          // Handle both boolean and array values
          const isChecked = Array.isArray(checked)
            ? checked.length > 0 && checked[0] === true
            : !!checked;
          if (!isChecked) {
            this.searchForm.get(`dynamicOutboundInputs.${option.value}`)?.setValue('');
          }
        });
      if (subscription) {
        this.subscriptions.add(subscription);
      }
    });

    // EDI Attributes filters
    this.sidebarEdiAttributesOptions.forEach(option => {
      const subscription = this.searchForm
        .get(`sidebarEdiAttributesFilters.${option.value}`)
        ?.valueChanges.subscribe(checked => {
          // Handle both boolean and array values
          const isChecked = Array.isArray(checked)
            ? checked.length > 0 && checked[0] === true
            : !!checked;
          if (!isChecked) {
            this.searchForm.get(`dynamicEdiAttributesInputs.${option.value}`)?.setValue('');
          }
        });
      if (subscription) {
        this.subscriptions.add(subscription);
      }
    });
  }

  // Getters for form arrays to use in template
  get inboundFieldsFormArray(): FormArray {
    return this.searchForm.get('inboundFields') as FormArray;
  }

  get outboundFieldsFormArray(): FormArray {
    return this.searchForm.get('outboundFields') as FormArray;
  }

  // Getter to check for date range validation errors
  get hasDateRangeError(): boolean {
    return this.searchForm.hasError('dateRangeInvalid');
  }

  // Getter to get the date range error message
  get dateRangeErrorMessage(): string {
    const error = this.searchForm.getError('dateRangeInvalid');
    return error?.message || '';
  }

  // TrackBy functions for better performance
  trackByValue = (_: number, item: any) => item.value;

  // Helper methods to check checkbox states
  isInboundCheckboxChecked(key: string): boolean {
    const value = this.searchForm.get('sidebarInboundFilters')?.get(key)?.value;
    // Handle both boolean and array values
    if (Array.isArray(value)) {
      return value.length > 0 && value[0] === true;
    }
    return !!value;
  }

  isOutboundCheckboxChecked(key: string): boolean {
    const value = this.searchForm.get('sidebarOutboundFilters')?.get(key)?.value;
    // Handle both boolean and array values
    if (Array.isArray(value)) {
      return value.length > 0 && value[0] === true;
    }
    return !!value;
  }

  isEdiCheckboxChecked(key: string): boolean {
    const value = this.searchForm.get('sidebarEdiAttributesFilters')?.get(key)?.value;
    // Handle both boolean and array values
    if (Array.isArray(value)) {
      return value.length > 0 && value[0] === true;
    }
    return !!value;
  }

  // Helper methods to check if any checkboxes are checked (for conditional styling)
  hasAnyInboundChecked(): boolean {
    return this.sidebarInboundOptions.some(option => this.isInboundCheckboxChecked(option.value));
  }

  hasAnyOutboundChecked(): boolean {
    return this.sidebarOutboundOptions.some(option => this.isOutboundCheckboxChecked(option.value));
  }

  hasAnyEdiAttributesChecked(): boolean {
    return this.sidebarEdiAttributesOptions.some(option => this.isEdiCheckboxChecked(option.value));
  }

  /**
   * Submit the search form
   */
  onSearch(): void {
    if (!this.searchForm.valid) {
      // Mark all fields as touched to show validation errors
      this.searchForm.markAllAsTouched();

      // If there's a date range error, we could show a toast or alert here
      if (this.hasDateRangeError) {
        console.warn('Date range validation error:', this.dateRangeErrorMessage);
      }

      return;
    }

    this.isLoading = true;
    const formValues = this.searchForm.value;

    // Save form values to the store
    this.store.dispatch(SearchActions.saveFormValues({ payload: formValues }));

    // Build query parameters
    const queryParams: Record<string, string> = {
      page: '1',
      pageSize: '20',
      sortBy: 'dateReceived',
      sortDirection: 'desc',
    };

    // Add search type filter
    if (formValues.searchType) {
      queryParams['filter_searchType'] = formValues.searchType;
    }

    // Add direction filter
    if (formValues.direction) {
      queryParams['filter_direction'] = formValues.direction;
    }

    // Add transaction view filter
    if (formValues.transactionView) {
      queryParams['filter_transactionView'] = formValues.transactionView;
    }

    // Add equipment IDs if present
    if (formValues.equipmentIds && formValues.equipmentIds.trim()) {
      queryParams['filter_equipmentIds'] = formValues.equipmentIds.trim();
      queryParams['filter_equipmentSearchType'] = formValues.equipmentSearchType;
    }

    // Add inbound fields if present
    formValues.inboundFields.forEach((field: any) => {
      if (field.value && field.value.trim()) {
        queryParams[`filter_${field.key}`] = field.value.trim();
      }
    });

    // Add outbound fields if present
    formValues.outboundFields.forEach((field: any) => {
      if (field.value && field.value.trim()) {
        queryParams[`filter_${field.key}`] = field.value.trim();
      }
    });

    // Add transaction attributes if present
    if (formValues.transactionAttributes && formValues.transactionAttributes.trim()) {
      queryParams['filter_transactionAttributes'] = formValues.transactionAttributes.trim();
    }

    // Add all sidebar inbound filters (both checked and unchecked)
    if (formValues.sidebarInboundFilters) {
      Object.entries(formValues.sidebarInboundFilters).forEach(([key, value]) => {
        queryParams[`filter_inbound_${key}`] = value ? 'true' : 'false';
      });
    }

    // Add all sidebar outbound filters (both checked and unchecked)
    if (formValues.sidebarOutboundFilters) {
      Object.entries(formValues.sidebarOutboundFilters).forEach(([key, value]) => {
        queryParams[`filter_outbound_${key}`] = value ? 'true' : 'false';
      });
    }

    // Add all sidebar EDI attributes filters (both checked and unchecked)
    if (formValues.sidebarEdiAttributesFilters) {
      Object.entries(formValues.sidebarEdiAttributesFilters).forEach(([key, value]) => {
        queryParams[`filter_edi_${key}`] = value ? 'true' : 'false';
      });
    }

    // Add dynamic input values for checked sidebar filters
    if (formValues.dynamicInboundInputs) {
      Object.entries(formValues.dynamicInboundInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_inbound_${key}`] = value.trim();
        }
      });
    }

    if (formValues.dynamicOutboundInputs) {
      Object.entries(formValues.dynamicOutboundInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_outbound_${key}`] = value.trim();
        }
      });
    }

    if (formValues.dynamicEdiAttributesInputs) {
      Object.entries(formValues.dynamicEdiAttributesInputs).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          queryParams[`filter_dynamic_edi_${key}`] = value.trim();
        }
      });
    }

    // Add start and end dates
    if (formValues.startDate) {
      const startMs = formValues.startDate.getTime();
      queryParams['filter_dateRangeStart'] = startMs.toString();
    }

    if (formValues.endDate) {
      const endMs = formValues.endDate.getTime();
      queryParams['filter_dateRangeEnd'] = endMs.toString();
    }

    // Save search parameters to the store
    this.store.dispatch(SearchActions.saveSearchParams({ payload: queryParams }));

    // Navigate to search results with query parameters
    this.router.navigate(['/search-results'], { queryParams });

    // Also call API directly to pre-fetch results
    const searchParams = this.searchService.parseSearchParams(queryParams);
    this.searchService.search(searchParams).subscribe(response => {
      this.isLoading = false;
      console.log('Search response:', response);
    });
  }

  /**
   * Reset the search form
   */
  onReset(): void {
    // Reset the form to initial values using the form's reset method
    this.searchForm.reset({
      searchType: 'edi',
      direction: 'inbound-outbound',
      transactionView: 'all',
      equipmentSearchType: 'equipment',
      equipmentIds: '',
      transactionAttributes: '',
      startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
      endDate: new Date(),
      // Include empty arrays for the form arrays to properly reset them
      inboundFields: this.inboundFields.map(field => ({
        label: field.label,
        placeholder: field.placeholder,
        value: '',
        key: field.key,
      })),
      outboundFields: this.outboundFields.map(field => ({
        label: field.label,
        placeholder: field.placeholder,
        value: '',
        key: field.key,
      })),
      // Reset sidebar filter checkboxes
      sidebarInboundFilters: {
        isaReceiver: false,
        gsReceiver: false,
      },
      sidebarOutboundFilters: {
        isaSender: false,
        gsSender: false,
      },
      sidebarEdiAttributesFilters: {
        messageId: false,
        network: false,
        messageStatus: false,
        controlNumber: false,
      },
      // Reset dynamic inputs
      dynamicInboundInputs: {
        isaReceiver: '',
        gsReceiver: '',
      },
      dynamicOutboundInputs: {
        isaSender: '',
        gsSender: '',
      },
      dynamicEdiAttributesInputs: {
        messageId: '',
        network: '',
        messageStatus: '',
        controlNumber: '',
      },
    });

    // Reset the store state
    this.store.dispatch(SearchActions.resetState());
  }

  /**
   * Update form values from the state
   */
  private updateFormFromState(formValues: any): void {
    if (!formValues) return;

    // Helper function to convert string 'true'/'false' to boolean
    const stringToBoolean = (value: any): boolean => {
      if (typeof value === 'boolean') return value;
      return value === 'true';
    };

    // Create new objects for sidebar filters instead of modifying existing ones
    // This avoids issues with read-only properties

    // Default values
    const defaultInboundFilters = {
      isaReceiver: false,
      gsReceiver: false,
    };

    const defaultOutboundFilters = {
      isaSender: false,
      gsSender: false,
    };

    const defaultEdiAttributesFilters = {
      messageId: false,
      network: false,
      messageStatus: false,
      controlNumber: false,
    };

    // Create new objects with converted values
    const sidebarInboundFilters = { ...defaultInboundFilters };
    const sidebarOutboundFilters = { ...defaultOutboundFilters };
    const sidebarEdiAttributesFilters = { ...defaultEdiAttributesFilters };

    // If we have values from the state, use them (with proper conversion)
    if (formValues.sidebarInboundFilters && typeof formValues.sidebarInboundFilters === 'object') {
      Object.keys(formValues.sidebarInboundFilters).forEach(key => {
        if (key in sidebarInboundFilters) {
          sidebarInboundFilters[key] = stringToBoolean(formValues.sidebarInboundFilters[key]);
        }
      });
    }

    if (
      formValues.sidebarOutboundFilters &&
      typeof formValues.sidebarOutboundFilters === 'object'
    ) {
      Object.keys(formValues.sidebarOutboundFilters).forEach(key => {
        if (key in sidebarOutboundFilters) {
          sidebarOutboundFilters[key] = stringToBoolean(formValues.sidebarOutboundFilters[key]);
        }
      });
    }

    if (
      formValues.sidebarEdiAttributesFilters &&
      typeof formValues.sidebarEdiAttributesFilters === 'object'
    ) {
      Object.keys(formValues.sidebarEdiAttributesFilters).forEach(key => {
        if (key in sidebarEdiAttributesFilters) {
          sidebarEdiAttributesFilters[key] = stringToBoolean(
            formValues.sidebarEdiAttributesFilters[key],
          );
        }
      });
    }

    // Update main form controls
    this.searchForm.patchValue(
      {
        searchType: formValues.searchType,
        direction: formValues.direction,
        transactionView: formValues.transactionView,
        equipmentSearchType: formValues.equipmentSearchType,
        equipmentIds: formValues.equipmentIds,
        transactionAttributes: formValues.transactionAttributes,
        startDate: formValues.startDate || this.startDate,
        endDate: formValues.endDate || this.endDate,
        // Update sidebar filter checkboxes with processed values
        sidebarInboundFilters,
        sidebarOutboundFilters,
        sidebarEdiAttributesFilters,
        // Update dynamic inputs if they exist in formValues
        dynamicInboundInputs: formValues.dynamicInboundInputs || {
          isaReceiver: '',
          gsReceiver: '',
        },
        dynamicOutboundInputs: formValues.dynamicOutboundInputs || {
          isaSender: '',
          gsSender: '',
        },
        dynamicEdiAttributesInputs: formValues.dynamicEdiAttributesInputs || {
          messageId: '',
          network: '',
          messageStatus: '',
          controlNumber: '',
        },
      },
      { emitEvent: false },
    );

    // Update inbound fields
    if (formValues.inboundFields && formValues.inboundFields.length > 0) {
      formValues.inboundFields.forEach((field: any, index: number) => {
        if (index < this.inboundFieldsFormArray.length) {
          this.inboundFieldsFormArray.at(index).patchValue(
            {
              value: field.value,
            },
            { emitEvent: false },
          );
        }
      });
    }

    // Update outbound fields
    if (formValues.outboundFields && formValues.outboundFields.length > 0) {
      formValues.outboundFields.forEach((field: any, index: number) => {
        if (index < this.outboundFieldsFormArray.length) {
          this.outboundFieldsFormArray.at(index).patchValue(
            {
              value: field.value,
            },
            { emitEvent: false },
          );
        }
      });
    }
  }
}
